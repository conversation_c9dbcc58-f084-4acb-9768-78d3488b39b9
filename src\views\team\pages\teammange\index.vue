<template>
  <div class="knowledge-management-container">
    <!-- 左侧导航 -->
    <div class="sidebar">
      <h2 class="sidebar-title">团队管理</h2>
      <div class="divider"></div>
      <div class="tab-list">
        <div 
          v-for="tab in tabs" 
          :key="tab.value"
          class="tab-item"
          :class="{ active: activeTab === tab.value }"
          @click="activeTab = tab.value"
        >
          {{ tab.label }}
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content">
      <!-- 数据标注 -->
      <div v-if="activeTab === 'permission'" class="content-section">
        
        <DataAnnotationTable />
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
// import TermTable from './TermTable.vue';
// import BusinessLogicTable from './BusinessLogicTable.vue';
// import CaseOptimizationTable from './CaseOptimizationTable.vue';
import DataAnnotationTable from './DataAnnotationTable.vue';
// import { useTheme } from '@chat/themechange';

// 使用主题composable
// const { defaultColor, changeTheme, initTheme } = useTheme();

// 定义标签页
const tabs = [
  { label: '数据权限管理', value: 'permission' }
];

// 当前激活的标签页
const activeTab = ref('permission');

// 主题色
// const primaryColor = ref(defaultColor.value);

// onMounted(() => {
//   // 监听微前端主题变更事件
//   window?.$wujie?.bus.$on('wujieTheme', (color: string) => {
//     console.log('收到主应用的主题颜色：', color);
//     primaryColor.value = color;
//     changeTheme(color);
//   });
  
//   // 初始化主题
//   initTheme();
// });
</script>

<style scoped lang="scss">
.knowledge-management-container {
  display: flex;
  height: calc(100vh - 62px);
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e6e8ee;
  margin-right: 8px;
}

.sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.sidebar-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  padding: 0 20px;
  margin: 0 0 15px 0;
}

.divider {
  height: 1px;
  background-color: #ebeef5;
  margin-bottom: 15px;
}

.tab-list {
  display: flex;
  flex-direction: column;
}

.tab-item {
  padding: 12px 20px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
  font-size: 14px;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    color: #409eff;
    background-color: #ecf5ff;
    border-right: 3px solid #409eff;
  }
}

.content {
  flex: 1;
  // padding: 20px;
  overflow-y: auto;
}

.content-section {
  background-color: #fff;
  border-radius: 8px;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 0px 20px 0 20px;
  height: calc(100vh - 64px);
}

.section-header {
  margin-bottom: 0px;
}

.section-header h2 {
  font-size: 17px;
  font-weight: bold;
  color: #303133;
  margin: 0 0 10px 0;
}

.description {
  font-size: 14px;
  color: #606266;
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.example-tip {
  background-color: #f0f9eb;
  border-radius: 4px;
  padding: 10px 15px;
  display: flex;
  align-items: flex-start;
  // margin-bottom: 20px;
  
  .el-icon {
    color: #67c23a;
    margin-right: 8px;
    margin-top: 2px;
  }
  
  span {
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
  }
}
</style>